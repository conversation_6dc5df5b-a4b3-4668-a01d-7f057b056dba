package com.xiaomi.mone.memory.service;

import com.xiaomi.mone.memory.api.dto.DemoReqDto;
import com.xiaomi.mone.memory.api.dto.DemoResDto;
import com.xiaomi.mone.memory.service.dao.DemoDao;
import com.xiaomi.mone.memory.service.dao.entity.DemoEntity;
import com.xiaomi.mone.memory.service.rpc.DemoDepRpc;
import com.xiaomi.mone.memory.service.rpc.dto.DemoDepReqDto;
import com.xiaomi.mone.memory.service.rpc.dto.DemoDepResDto;
import com.xiaomi.youpin.infra.rpc.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DemoService {

    @Autowired
    private DemoDepRpc demoDepRpc;
    @Autowired
    private DemoDao demoDao;

    public Result<DemoResDto> query(DemoReqDto reqDto) {
        //rpc远程调用
        DemoDepResDto rpcResDto = demoDepRpc.remoteReq(DemoDepReqDto.build(reqDto));
        //持久化实体查询
        DemoEntity entity = demoDao.getById(reqDto.getId());
        return Result.success(entity.buildDemoResDto(rpcResDto));
    }
}
